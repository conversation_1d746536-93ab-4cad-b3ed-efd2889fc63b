bt_navigator:
  ros__parameters:
    global_frame: map
    robot_base_frame: base_link
    odom_topic: /odom
    bt_loop_duration: 10
    filter_duration: 0.3
    default_server_timeout: 20
    wait_for_service_timeout: 15000  # 增加等待服务超时时间到60秒
    action_server_result_timeout: 900.0
    navigators: ["navigate_to_pose", "navigate_through_poses"]
    navigate_to_pose:
      plugin: "nav2_bt_navigator::NavigateToPoseNavigator"
    navigate_through_poses:
      plugin: "nav2_bt_navigator::NavigateThroughPosesNavigator"
    # 'default_nav_through_poses_bt_xml' and 'default_nav_to_pose_bt_xml' are use defaults:
    # nav2_bt_navigator/navigate_to_pose_w_replanning_and_recovery.xml
    # nav2_bt_navigator/navigate_through_poses_w_replanning_and_recovery.xml
    # They can be set here or via a RewrittenYaml remap from a parent launch file to Nav2.

    # 错误代码名称前缀，确保包含所有必要的行为
    error_code_name_prefixes:
      - assisted_teleop
      - backup
      - compute_path
      - drive_on_heading
      - follow_path
      - nav_thru_poses
      - nav_to_pose
      - spin
      - wait

    # plugin_lib_names is used to add custom BT plugins to the executor (vector of strings).
    # Built-in plugins are added automatically
    # plugin_lib_names: []

controller_server:
  ros__parameters:
    controller_frequency: 15.0  # 降低控制器频率从20Hz到15Hz
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.5
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.3
    progress_checker_plugins: ["progress_checker"]
    goal_checker_plugins: ["general_goal_checker"] # "precise_goal_checker"
    controller_plugins: ["FollowPath"]

    # Progress checker parameters
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.5
      movement_time_allowance: 10.0
    # Goal checker parameters
    #precise_goal_checker:
    #  plugin: "nav2_controller::SimpleGoalChecker"
    #  xy_goal_tolerance: 0.25
    #  yaw_goal_tolerance: 0.25
    #  stateful: True
    general_goal_checker:
      stateful: True
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.25
      yaw_goal_tolerance: 0.25
    # DWB parameters
    FollowPath:
      plugin: "nav2_mppi_controller::MPPIController"
      time_steps: 40           # 减少时间步长从56到40
      model_dt: 0.0667         # 设置为控制器周期的倒数 (1/15.0)
      batch_size: 1000         # 减少批量大小从2000到1000
      ax_max: 3.0
      ax_min: -3.0
      ay_max: 3.0
      ay_min: -3.0
      az_max: 3.5
      vx_std: 0.2
      vy_std: 0.2
      wz_std: 0.4
      vx_max: 0.5
      vx_min: -0.35
      vy_max: 0.5
      wz_max: 1.9
      iteration_count: 1
      prune_distance: 1.7
      transform_tolerance: 0.1
      temperature: 0.3
      gamma: 0.015
      motion_model: "DiffDrive"
      visualize: false         # 关闭可视化以减少计算负担
      regenerate_noises: true
      TrajectoryVisualizer:
        trajectory_step: 5
        time_step: 3
      AckermannConstraints:
        min_turning_r: 0.2
      critics: [
        "ConstraintCritic", "CostCritic", "GoalCritic",
        "GoalAngleCritic", "PathAlignCritic", "PathFollowCritic",
        "PathAngleCritic", "PreferForwardCritic"]
      ConstraintCritic:
        enabled: true
        cost_power: 1
        cost_weight: 4.0
      GoalCritic:
        enabled: true
        cost_power: 1
        cost_weight: 5.0
        threshold_to_consider: 1.4
      GoalAngleCritic:
        enabled: true
        cost_power: 1
        cost_weight: 3.0
        threshold_to_consider: 0.5
      PreferForwardCritic:
        enabled: true
        cost_power: 1
        cost_weight: 5.0
        threshold_to_consider: 0.5
      CostCritic:
        enabled: true
        cost_power: 1
        cost_weight: 3.81
        near_collision_cost: 253
        critical_cost: 300.0
        consider_footprint: false
        collision_cost: 1000000.0
        near_goal_distance: 1.0
        trajectory_point_step: 2
      PathAlignCritic:
        enabled: true
        cost_power: 1
        cost_weight: 14.0
        max_path_occupancy_ratio: 0.05
        trajectory_point_step: 4
        threshold_to_consider: 0.5
        offset_from_furthest: 20
        use_path_orientations: false
      PathFollowCritic:
        enabled: true
        cost_power: 1
        cost_weight: 5.0
        offset_from_furthest: 5
        threshold_to_consider: 1.4
      PathAngleCritic:
        enabled: true
        cost_power: 1
        cost_weight: 2.0
        offset_from_furthest: 4
        threshold_to_consider: 0.5
        max_angle_to_furthest: 1.0
        mode: 0
      # TwirlingCritic:
      #   enabled: true
      #   twirling_cost_power: 1
      #   twirling_cost_weight: 10.0

local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 1.5  # 降低更新频率
      publish_frequency: 1.0
      global_frame: odom
      robot_base_frame: base_link
      rolling_window: true
      width: 3
      height: 3
      resolution: 0.05
      robot_radius: 0.22
      plugins: ["voxel_layer", "inflation_layer"]
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.55
      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: True
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 2.0
        mark_threshold: 0
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
      always_send_full_costmap: True

global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 0.3  # 降低全局代价地图更新频率
      publish_frequency: 0.3  # 降低发布频率
      global_frame: map
      robot_base_frame: base_link
      robot_radius: 0.22
      resolution: 0.1
      rolling_window: True
      width: 300
      height: 300
      # origin_x: 0.0
      # origin_y: 0.0
      track_unknown_space: true
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
          # outdoors there will probably be more inf points
          inf_is_valid: true
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.55
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
        enabled: true  #
        track_unknown_space: false  # 未知空间视为自由空间
        use_maximum: false  # 使用覆盖模式而非最大值模式
      always_send_full_costmap: True

map_server:
  ros__parameters:
    yaml_filename: "/opt/overlay_ws/src/navigation2/nav2_system_tests/maps/blank_map_1000x1000.yaml"

map_saver:
  ros__parameters:
    save_map_timeout: 5.0
    free_thresh_default: 0.25
    occupied_thresh_default: 0.65
    map_subscribe_transient_local: True

amcl:
  ros__parameters:
    use_sim_time: True

    # 基础框架配置
    base_frame_id: "base_link"
    global_frame_id: "map"
    odom_frame_id: "odom"

    # 消息过滤器优化 - 解决队列满问题的核心配置
    tf_message_filter_queue_size: 10
    transform_tolerance: 1.0  # 增加变换容忍度

    # 激光雷达处理优化
    max_beams: 60  # 从60减少到30，提高处理速度
    laser_model_type: "likelihood_field"  # 使用高效的激光模型
    laser_max_range: 100.0
    laser_min_range: -1.0
    laser_likelihood_max_dist: 2.0

    # 粒子滤波器配置
    max_particles: 2000  # 从2000减少到1500，提高处理速度
    min_particles: 500   # 从500减少到300
    pf_err: 0.05
    pf_z: 0.99

    # 运动模型参数
    alpha1: 0.2
    alpha2: 0.2
    alpha3: 0.2
    alpha4: 0.2
    alpha5: 0.2
    robot_model_type: "nav2_amcl::DifferentialMotionModel"

    # 更新阈值优化 - 更频繁的更新以适应GPS导航
    update_min_a: 0.05  # 降低角度更新阈值
    update_min_d: 0.05  # 降低距离更新阈值

    # 重采样配置
    resample_interval: 1
    recovery_alpha_fast: 0.0
    recovery_alpha_slow: 0.0

    # 传感器模型参数
    z_hit: 0.5
    z_short: 0.05
    z_max: 0.05
    z_rand: 0.5
    sigma_hit: 0.2
    lambda_short: 0.1

    # 光束跳跃检测
    do_beamskip: false
    beam_skip_distance: 0.5
    beam_skip_threshold: 0.3
    beam_skip_error_threshold: 0.9

    # 话题配置
    scan_topic: scan
    map_topic: map

    # 初始位置配置
    set_initial_pose: true  # 不使用固定初始位置
    initial_pose:
      x: 0.0
      y: 0.0
      z: 0.0
      yaw: 0.0

    # 其他配置
    tf_broadcast: true
    save_pose_rate: 0.5
    first_map_only: false
    freespace_downsampling: false

planner_server:
  ros__parameters:
    expected_planner_frequency: 10.0  # 降低期望频率从20Hz到10Hz
    planner_plugins: ["GridBased"]
    GridBased:
      plugin: "nav2_navfn_planner::NavfnPlanner"
      tolerance: 0.5
      use_astar: false
      allow_unknown: true

smoother_server:
  ros__parameters:
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: True
    # QoS设置，用于解决RTPS_READER_HISTORY错误
    qos_overrides:
      /parameter_events:
        publisher:
          depth: 1000
          durability: volatile
          history: keep_last
          reliability: reliable
      /tf:
        publisher:
          depth: 100
          durability: volatile
          history: keep_last
          reliability: reliable
      /tf_static:
        publisher:
          depth: 100
          durability: transient_local
          history: keep_last
          reliability: reliable
      default:
        publisher:
          depth: 50
          durability: volatile
          history: keep_last
          reliability: reliable
        subscription:
          depth: 50
          durability: volatile
          history: keep_last
          reliability: reliable

behavior_server:
  ros__parameters:
    local_costmap_topic: local_costmap/costmap_raw
    global_costmap_topic: global_costmap/costmap_raw
    local_footprint_topic: local_costmap/published_footprint
    global_footprint_topic: global_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "assisted_teleop", "wait"]
    spin:
      plugin: "nav2_behaviors::Spin"
    backup:
      plugin: "nav2_behaviors::BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors::DriveOnHeading"
    wait:
      plugin: "nav2_behaviors::Wait"
    assisted_teleop:
      plugin: "nav2_behaviors::AssistedTeleop"
    local_frame: odom
    global_frame: map
    robot_base_frame: base_link
    transform_tolerance: 0.1
    simulate_ahead_time: 2.0
    max_rotational_vel: 1.0
    min_rotational_vel: 0.4
    rotational_acc_lim: 3.2

waypoint_follower:
  ros__parameters:
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 200

velocity_smoother:
  ros__parameters:
    smoothing_frequency: 20.0
    scale_velocities: False
    feedback: "OPEN_LOOP"
    max_velocity: [0.26, 0.0, 1.0]
    min_velocity: [-0.26, 0.0, -1.0]
    max_accel: [2.5, 0.0, 3.2]
    max_decel: [-2.5, 0.0, -3.2]
    odom_topic: "odom"
    odom_duration: 0.1
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 1.0

collision_monitor:
  ros__parameters:
    base_frame_id: "base_link"
    odom_frame_id: "odom"
    cmd_vel_in_topic: "cmd_vel_smoothed"
    cmd_vel_out_topic: "cmd_vel"
    state_topic: "collision_monitor_state"
    transform_tolerance: 0.2
    source_timeout: 1.0
    base_shift_correction: True
    stop_pub_timeout: 2.0
    # Polygons represent zone around the robot for "stop", "slowdown" and "limit" action types,
    # and robot footprint for "approach" action type.
    polygons: ["FootprintApproach"]
    FootprintApproach:
      type: "polygon"
      action_type: "approach"
      footprint_topic: "/local_costmap/published_footprint"
      time_before_collision: 2.0
      simulation_time_step: 0.1
      min_points: 6
      visualize: False
      enabled: True
    observation_sources: ["scan"]
    scan:
      type: "scan"
      topic: "scan"
      min_height: 0.15
      max_height: 2.0
      enabled: True

# 注释掉对接节点配置
# docking_server:
#   ros__parameters:
#     controller_frequency: 50.0
#     initial_perception_timeout: 5.0
#     wait_charge_timeout: 5.0
#     dock_approach_timeout: 30.0
#     undock_linear_tolerance: 0.05
#     undock_angular_tolerance: 0.1
#     max_retries: 3
#     base_frame: "base_link"
#     fixed_frame: "odom"
#     dock_backwards: false
#     dock_prestaging_tolerance: 0.5
#
#     # Types of docks
#     dock_plugins: ['simple_charging_dock']
#     simple_charging_dock:
#       plugin: 'opennav_docking::SimpleChargingDock'
#       docking_threshold: 0.05
#       staging_x_offset: -0.7
#       use_external_detection_pose: true
#       use_battery_status: false # true
#       use_stall_detection: false # true
#
#       external_detection_timeout: 1.0
#       external_detection_translation_x: -0.18
#       external_detection_translation_y: 0.0
#       external_detection_rotation_roll: -1.57
#       external_detection_rotation_pitch: -1.57
#       external_detection_rotation_yaw: 0.0
#       filter_coef: 0.1
#
#     # Dock instances
#     docks: ['home_dock']  # Input your docks here
#     home_dock:
#       type: 'simple_charging_dock'
#       frame: map
#       pose: [0.0, 0.0, 0.0]
#
#     controller:
#       k_phi: 3.0
#       k_delta: 2.0
#       v_linear_min: 0.15
#       v_linear_max: 0.15

# 地图管理节点参数
map_manager:
  ros__parameters:
    indoor_areas_config: "/opt/overlay_ws/src/navigation2/nav2_system_tests/src/gps_navigation/config/indoor_areas_config.yaml"
    map_update_interval: 1.0
    tf_broadcast_interval: 0.1
    debug_level: "info"
    blank_map_path: "/opt/overlay_ws/src/navigation2/nav2_system_tests/maps/blank_map_1000x1000.yaml"

# 环境检测器节点参数
environment_detector:
  ros__parameters:
    use_sim_time: True
    check_interval: 1.0  # 检查间隔，单位：秒
    indoor_areas_config: "/opt/overlay_ws/src/navigation2/nav2_system_tests/src/gps_navigation/config/indoor_areas_config.yaml"
    entrance_detection_radius: 1.5  # 入口检测半径，单位：米
    state_file_path: "/tmp/environment_state.json"  # 状态文件保存路径
    position_change_threshold: 50.0  # 位置变化阈值，单位：米
    force_check_on_startup: True  # 启动时是否强制检查环境
    debug_level: "debug"  # 日志级别：debug, info, warn
