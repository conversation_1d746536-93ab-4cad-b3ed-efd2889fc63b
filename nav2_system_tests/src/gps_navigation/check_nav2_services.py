#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
检查Nav2服务状态的诊断脚本
"""

import rclpy
from rclpy.node import Node
import time


class Nav2ServiceChecker(Node):
    def __init__(self):
        super().__init__('nav2_service_checker')

        # 需要检查的服务列表（实际的ROS2服务）
        self.required_services = [
            '/is_path_valid',  # planner_server提供的服务
        ]

        # 需要检查的动作列表（这些是动作，不是服务）
        self.required_actions = [
            '/compute_path_to_pose',
            '/compute_path_through_poses',
            '/smooth_path',
            '/follow_path',
            '/navigate_to_pose',
            '/navigate_through_poses',
            '/backup',
            '/spin',
            '/wait',
            '/drive_on_heading',
            '/assisted_teleop',
        ]

        self.get_logger().info("Nav2服务检查器已启动")

    def check_services(self):
        """检查所有必需的服务和动作"""
        self.get_logger().info("开始检查Nav2服务和动作...")

        # 检查常规服务
        available_services = []
        missing_services = []

        service_names = self.get_service_names_and_types()
        available_service_names = [name for name, _ in service_names]

        for service in self.required_services:
            if service in available_service_names:
                available_services.append(service)
                self.get_logger().info(f"✅ 服务可用: {service}")
            else:
                missing_services.append(service)
                self.get_logger().warning(f"❌ 服务缺失: {service}")

        # 检查动作服务器（通过检查动作相关的服务）
        available_actions = []
        missing_actions = []

        for action in self.required_actions:
            # 检查动作服务器是否存在（通过检查其相关服务）
            action_services = [
                f"{action}/_action/send_goal",
                f"{action}/_action/cancel_goal",
                f"{action}/_action/get_result"
            ]

            action_available = all(service in available_service_names for service in action_services)

            if action_available:
                available_actions.append(action)
                self.get_logger().info(f"✅ 动作可用: {action}")
            else:
                missing_actions.append(action)
                self.get_logger().warning(f"❌ 动作缺失: {action}")

        # 总结
        total_available = len(available_services) + len(available_actions)
        total_missing = len(missing_services) + len(missing_actions)

        self.get_logger().info(f"服务和动作检查完成:")
        self.get_logger().info(f"  可用服务: {len(available_services)}")
        self.get_logger().info(f"  可用动作: {len(available_actions)}")
        self.get_logger().info(f"  缺失服务: {len(missing_services)}")
        self.get_logger().info(f"  缺失动作: {len(missing_actions)}")

        if missing_services or missing_actions:
            self.get_logger().error("以下服务/动作缺失，可能导致导航失败:")
            for service in missing_services:
                self.get_logger().error(f"  - 服务: {service}")
            for action in missing_actions:
                self.get_logger().error(f"  - 动作: {action}")
        else:
            self.get_logger().info("🎉 所有必需的服务和动作都可用!")

        return total_missing == 0

    def check_nodes(self):
        """检查Nav2节点状态"""
        self.get_logger().info("检查Nav2节点...")

        required_nodes = [
            '/controller_server',
            '/planner_server',
            '/smoother_server',
            '/behavior_server',
            '/bt_navigator',
            '/waypoint_follower',
            '/velocity_smoother',
            '/collision_monitor',
            '/lifecycle_manager_navigation',
        ]

        node_names = self.get_node_names()

        available_nodes = []
        missing_nodes = []

        for node in required_nodes:
            node_name = node.lstrip('/')  # 移除前导斜杠
            if node_name in node_names:
                available_nodes.append(node)
                self.get_logger().info(f"✅ 节点运行: {node}")
            else:
                missing_nodes.append(node)
                self.get_logger().warning(f"❌ 节点缺失: {node}")

        self.get_logger().info(f"节点检查完成:")
        self.get_logger().info(f"  运行节点: {len(available_nodes)}")
        self.get_logger().info(f"  缺失节点: {len(missing_nodes)}")

        return len(missing_nodes) == 0


def main():
    rclpy.init()

    checker = Nav2ServiceChecker()
    exit_code = 1  # 默认失败

    try:
        # 等待一段时间让系统稳定
        checker.get_logger().info("等待1秒让系统稳定...")
        time.sleep(1.0)

        # 检查节点
        nodes_ok = checker.check_nodes()

        # 等待一下
        time.sleep(2.0)

        # 检查服务
        services_ok = checker.check_services()

        if nodes_ok and services_ok:
            checker.get_logger().info("🎉 Nav2系统检查通过!")
            exit_code = 0
        else:
            checker.get_logger().error("❌ Nav2系统检查失败!")
            exit_code = 1

    except KeyboardInterrupt:
        checker.get_logger().info("检查被用户中断")
        exit_code = 1
    except Exception as e:
        checker.get_logger().error(f"检查过程中发生错误: {e}")
        exit_code = 1
    finally:
        checker.destroy_node()
        rclpy.shutdown()
        exit(exit_code)


if __name__ == '__main__':
    main()
